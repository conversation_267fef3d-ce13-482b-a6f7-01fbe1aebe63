import * as express from "express";
import {
    auditable,
    authenticate,
    authorize,
    decodePid,
    getEntity,
    getEntityPathFromQuery,
    validate
} from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import { getDynamicDomainService, getStaticDomainService } from "../services/domain";
import { getEntityStaticDomainService } from "../services/entityStaticDomainService";
import { getEntityDynamicDomainService } from "../services/entityDynamicDomainService";
import { BaseEntity } from "../entities/entity";
import { DomainStatus, StaticDomain, StaticDomainType } from "../entities/domain";
import { parseFilter } from "../services/filter";
import * as FilterService from "../services/filter";

const router: express.Router = express.Router();

// Helper function to add backward compatibility fields for lobby domains
function addBackwardCompatibilityFields(domain: StaticDomain): StaticDomain & { name: string; isActive: boolean } {
    return {
        ...domain,
        name: domain.domain, // domain -> name for backward compatibility
        isActive: domain.status === DomainStatus.ACTIVE // status -> isActive for backward compatibility
    };
}

const DOMAIN_DEFAULT_SORT_KEY = "createdAt";
const DOMAIN_KEYS = ["domain", "description", "expiryDate", "status", "updatedAt", DOMAIN_DEFAULT_SORT_KEY];

// Reusable validation objects
const PAGINATION_VALIDATION = {
    limit: { optional: true, isInt: { options: { min: 1, max: FilterService.MAX_LIMIT } } },
    offset: { optional: true, isInt: { options: { min: 0 } } }
};

const DOMAIN_STATUS_VALIDATION = {
    status: { optional: true, isIn: { options: [[DomainStatus.ACTIVE, DomainStatus.SUSPENDED]] } },
    status__in: { optional: true, isString: true },
    status__ne: { optional: true, isIn: { options: [[DomainStatus.ACTIVE, DomainStatus.SUSPENDED]] } }
};

const STATIC_DOMAIN_TYPE_VALIDATION = {
    type: { optional: true, isIn: { options: [[StaticDomainType.STATIC, StaticDomainType.LOBBY, StaticDomainType.LIVE_STREAMING, StaticDomainType.EHUB]] } }
};

const STATIC_DOMAIN_TYPE_FILTER_VALIDATION = {
    type: { optional: true, isIn: { options: [[StaticDomainType.STATIC, StaticDomainType.LOBBY, StaticDomainType.LIVE_STREAMING, StaticDomainType.EHUB]] } },
    type__in: { optional: true, isString: true },
    type__ne: { optional: true, isIn: { options: [[StaticDomainType.STATIC, StaticDomainType.LOBBY, StaticDomainType.LIVE_STREAMING, StaticDomainType.EHUB]] } }
};

const DOMAIN_COMMON_FIELDS_VALIDATION = {
    description: { optional: true, isString: true },
    provider: { optional: true, isString: true },
    expiryDate: { optional: true, isISO8601: true }
};

const DOMAIN_FILTER_VALIDATION = {
    domain: { optional: true, isString: true },
    domain__contains: { optional: true, isString: true },
    domain__in: { optional: true, isString: true },
    description: { optional: true, isString: true },
    description__contains: { optional: true, isString: true },
    description__in: { optional: true, isString: true },
    expiryDate: { optional: true, isISO8601: true },
    expiryDate__gte: { optional: true, isISO8601: true },
    expiryDate__lte: { optional: true, isISO8601: true },
    expiryDate__gt: { optional: true, isISO8601: true },
    expiryDate__lt: { optional: true, isISO8601: true }
};

async function getDynamicDomains(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const keys = ["environment", ...DOMAIN_KEYS];
        const offset = FilterService.valueFromQuery(req.query, "offset");
        const limit = FilterService.valueFromQuery(req.query, "limit");
        const sortBy = FilterService.getSortKey(req.query, keys, DOMAIN_DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(req.query, "sortOrder") || "ASC";
        const domains = await getDynamicDomainService().find({
            where: parseFilter(req.query, keys),
            offset,
            limit,
            order: [[sortBy, sortOrder]],
        });
        res.send(domains);
        next();
    } catch (err) {
        next(err);
    }
}

async function createDynamicDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const domain = await getDynamicDomainService().create(req.body);
        res.status(201).send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function getDynamicDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const domain = await getDynamicDomainService().findOne(req.params.id);
        res.send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function updateDynamicDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const update = req.body;
        update.id = req.params.id;
        const domain = await getDynamicDomainService().update(update);
        res.send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function removeDynamicDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        await getDynamicDomainService().remove(req.params.id);
        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

async function getStaticDomains(req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) {
    try {
        let entity: BaseEntity;
        if (req.query.path) {
            entity = req.keyEntity.find({ path: getEntityPathFromQuery(req) });
        }
        const keys = ["type", ...DOMAIN_KEYS];
        const offset = FilterService.valueFromQuery(req.query, "offset");
        const limit = FilterService.valueFromQuery(req.query, "limit");
        const sortBy = FilterService.getSortKey(req.query, keys, DOMAIN_DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(req.query, "sortOrder") || "ASC";
        const domains = await getStaticDomainService().find({
            entity,
            where: parseFilter(req.query, keys),
            offset,
            limit,
            order: [[sortBy, sortOrder]],
        });
        res.send(domains);
        next();
    } catch (err) {
        next(err);
    }
}

async function createStaticDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const domain = await getStaticDomainService().create(req.body);
        res.status(201).send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function getStaticDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const domain = await getStaticDomainService().findOne(req.params.id);
        res.send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function updateStaticDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const update = req.body;
        update.id = req.params.id;
        const domain = await getStaticDomainService().update(update);
        res.send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function removeStaticDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        await getStaticDomainService().remove(req.params.id);
        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

async function getDynamicEntityDomain(req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction) {
    try {
        const entity = getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });
        const domain = await getEntityDynamicDomainService().get(entity);
        if (!domain) {
            res.status(204).end();
        } else {
            res.send(domain);
        }
        next();
    } catch (err) {
        next(err);
    }
}

async function setDynamicEntityDomain(req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const domain = await getEntityDynamicDomainService().set(entity, req.params.id);
        res.send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function resetDynamicEntityDomain(req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const domain = await getEntityDynamicDomainService().reset(entity);
        if (!domain) {
            res.status(204).end();
        } else {
            res.send(domain);
        }
        next();
    } catch (err) {
        next(err);
    }
}

async function getStaticEntityDomain(req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction) {
    try {
        const entity = getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });
        const domainType = req.query.type as StaticDomainType || StaticDomainType.STATIC;
        const domain = await getEntityStaticDomainService().get(entity, domainType);
        if (!domain) {
            res.status(204).end();
        } else {
            res.send(domain);
        }
        next();
    } catch (err) {
        next(err);
    }
}

async function setStaticEntityDomain(req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const domainType = req.body.type as StaticDomainType || StaticDomainType.STATIC;
        const domain = await getEntityStaticDomainService().set(entity, req.params.id, domainType);
        res.send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function resetStaticEntityDomain(req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const domainType = req.body.type as StaticDomainType || StaticDomainType.STATIC;
        const domain = await getEntityStaticDomainService().reset(entity, domainType);
        if (!domain) {
            res.status(204).end();
        } else {
            res.send(domain);
        }
        next();
    } catch (err) {
        next(err);
    }
}

const setStaticDomainTagsHandler = async (req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction) => {
    try {
        const entity = getEntity(req);
        const service = getEntityStaticDomainService();
        const result = await service.setTags(entity, req.body.tags);
        res.send(result);
    } catch (err) {
        next(err);
    }
};

const resetStaticDomainTagsHandler = async (req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction) => {
    try {
        const entity = getEntity(req);
        const service = getEntityStaticDomainService();
        const result = await service.resetTags(entity);
        res.send(result);
    } catch (err) {
        next(err);
    }
};

// dynamic domain management
router.get("/domains/dynamic", authenticate, authorize,
    validate({
        ...PAGINATION_VALIDATION,
        // Filter parameters for dynamic domains
        environment: { optional: true, isWord: true },
        environment__contains: { optional: true, isString: true },
        environment__in: { optional: true, isString: true },
        ...DOMAIN_FILTER_VALIDATION,
        ...DOMAIN_STATUS_VALIDATION
    }),
    getDynamicDomains);
router.post("/domains/dynamic", authenticate, authorize,
    validate({
        domain: { notEmpty: true, isDomain: true },
        environment: { notEmpty: true, isWord: true },
        ...DOMAIN_COMMON_FIELDS_VALIDATION,
        status: { optional: true, isIn: { options: [[DomainStatus.ACTIVE, DomainStatus.SUSPENDED]] } }
    }),
    auditable,
    createDynamicDomain);
router.get("/domains/dynamic/:id", authenticate, authorize, decodePid(), getDynamicDomain);
router.patch("/domains/dynamic/:id", authenticate, authorize,
    validate({
        domain: { optional: true, isDomain: true },
        environment: { optional: true, isWord: true },
        ...DOMAIN_COMMON_FIELDS_VALIDATION,
        status: { optional: true, isIn: { options: [[DomainStatus.ACTIVE, DomainStatus.SUSPENDED]] } }
    }),
    auditable,
    decodePid(),
    updateDynamicDomain);
router.delete("/domains/dynamic/:id", authenticate, authorize, decodePid(), auditable, removeDynamicDomain);

// static domain management
router.get("/domains/static", authenticate, authorize,
    validate({
        ...PAGINATION_VALIDATION,
        ...STATIC_DOMAIN_TYPE_FILTER_VALIDATION,
        ...DOMAIN_FILTER_VALIDATION,
        ...DOMAIN_STATUS_VALIDATION,
        // Support for entity path filtering (existing functionality)
        path: { optional: true, isString: true }
    }),
    getStaticDomains);
router.post("/domains/static", authenticate, authorize,
    validate({
        domain: { notEmpty: true, isStaticDomain: true },
        ...DOMAIN_COMMON_FIELDS_VALIDATION,
        status: { optional: true, isIn: { options: [[DomainStatus.ACTIVE, DomainStatus.SUSPENDED]] } },
        ...STATIC_DOMAIN_TYPE_VALIDATION
    }),
    auditable,
    createStaticDomain);
router.get("/domains/static/:id", authenticate, authorize, decodePid(), getStaticDomain);
router.patch("/domains/static/:id", authenticate, authorize,
    validate({
        domain: { optional: true, isStaticDomain: true },
        ...DOMAIN_COMMON_FIELDS_VALIDATION,
        status: { optional: true, isIn: { options: [[DomainStatus.ACTIVE, DomainStatus.SUSPENDED]] } },
        type: { optional: true, isIn: { options: [["Static", "Lobby", "Live Streaming", "Ehub"]] } }
    }),
    decodePid(),
    auditable,
    updateStaticDomain);
router.delete("/domains/static/:id", authenticate, authorize, decodePid(), auditable, removeStaticDomain);

router.get("/domains/lobby",
    authenticate,
    authorize,
    async function (req, res, next) {
        try {
            const domains = await getStaticDomainService().findAll({ type: StaticDomainType.LOBBY });
            const domainsWithBackwardCompatibility = domains.map(addBackwardCompatibilityFields);
            res.send(domainsWithBackwardCompatibility);
            next();
        } catch (err) {
            next(err);
        }
    });

router.post("/domains/lobby",
    authenticate,
    authorize,
    validate({
        domain: { optional: true, isLobbyDomainTemplate: true },
        name: { optional: true, isLobbyDomainTemplate: true }, // backward compatibility: name -> domain
        ...DOMAIN_COMMON_FIELDS_VALIDATION,
        status: { optional: true, isIn: { options: [[DomainStatus.ACTIVE, DomainStatus.SUSPENDED]] } },
        isActive: { optional: true, isBoolean: true } // backward compatibility: isActive -> status
    }),
    auditable,
    async function (req, res, next) {
        try {
            // Handle backward compatibility: map name -> domain and isActive -> status
            const requestData = { ...req.body };

            // Ensure we have a domain field (either from domain or name)
            if (requestData.name && !requestData.domain) {
                requestData.domain = requestData.name;
            } else if (!requestData.domain && !requestData.name) {
                return res.status(400).json({ error: "Either 'domain' or 'name' field is required" });
            }

            if (requestData.isActive !== undefined && requestData.status === undefined) {
                requestData.status = requestData.isActive ? DomainStatus.ACTIVE : DomainStatus.SUSPENDED;
            }

            const domain = await getStaticDomainService().create({ ...requestData, type: StaticDomainType.LOBBY });
            const domainWithBackwardCompatibility = addBackwardCompatibilityFields(domain);
            res.status(201).send(domainWithBackwardCompatibility);
            next();
        } catch (err) {
            next(err);
        }
    });

router.get("/domains/lobby/:id",
    authenticate,
    authorize,
    decodePid(),
    async function (req, res, next) {
        try {
            const domain = await getStaticDomainService().findOne(req.params.id);
            const domainWithBackwardCompatibility = addBackwardCompatibilityFields(domain);
            res.send(domainWithBackwardCompatibility);
            next();
        } catch (err) {
            next(err);
        }
    });

router.patch("/domains/lobby/:id",
    authenticate,
    authorize,
    validate({
        domain: { optional: true, isLobbyDomainTemplate: true },
        name: { optional: true, isLobbyDomainTemplate: true }, // backward compatibility: name -> domain
        status: { optional: true, isIn: { options: [[DomainStatus.ACTIVE, DomainStatus.SUSPENDED]] } },
        isActive: { optional: true, isBoolean: true }, // backward compatibility: isActive -> status
    }),
    decodePid(),
    auditable,
    async function (req, res, next) {
        try {
            // Handle backward compatibility: map name -> domain and isActive -> status
            const requestData = { ...req.body };

            // Handle backward compatibility for domain field
            if (requestData.name && !requestData.domain) {
                requestData.domain = requestData.name;
            }

            if (requestData.isActive !== undefined && requestData.status === undefined) {
                requestData.status = requestData.isActive ? DomainStatus.ACTIVE : DomainStatus.SUSPENDED;
            }

            const domain = await getStaticDomainService().update({ id: req.params.id, ...requestData }, StaticDomainType.LOBBY);
            const domainWithBackwardCompatibility = addBackwardCompatibilityFields(domain);
            res.send(domainWithBackwardCompatibility);
            next();
        } catch (err) {
            next(err);
        }
    });

router.delete("/domains/lobby/:id",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async function (req, res, next) {
        try {
            await getStaticDomainService().remove(req.params.id);
            res.status(204).end();
            next();
        } catch (err) {
            next(err);
        }
    });

// entity dynamic domain
router.put("/:path/entitydomain/dynamic/:id", authenticate, authorize, decodePid(), auditable, setDynamicEntityDomain);
router.get("/:path/entitydomain/dynamic", authenticate, authorize, decodePid(), getDynamicEntityDomain);
router.delete("/:path/entitydomain/dynamic", authenticate, authorize, decodePid(), auditable, resetDynamicEntityDomain);
router.put("/entitydomain/dynamic/:id", authenticate, authorize, decodePid(), auditable, setDynamicEntityDomain);
router.get("/entitydomain/dynamic", authenticate, authorize, decodePid(), getDynamicEntityDomain);
router.delete("/entitydomain/dynamic", authenticate, authorize, decodePid(), auditable, resetDynamicEntityDomain);

// entity static domain
router.put("/:path/entitydomain/static/tags",
    authenticate,
    authorize,
    validate({
        tags: {
            notEmpty: true,
            isDomainArray: { errorMessage: "should be an array of domains" }
        }
    }),
    auditable,
    setStaticDomainTagsHandler);

router.delete("/:path/entitydomain/static/tags",
    authenticate,
    authorize,
    auditable,
    resetStaticDomainTagsHandler);


const validateStaticDomainType = validate(STATIC_DOMAIN_TYPE_VALIDATION);

router.put("/:path/entitydomain/static/:id", authenticate, authorize, decodePid(), auditable, validateStaticDomainType, setStaticEntityDomain);
router.get("/:path/entitydomain/static", authenticate, authorize, decodePid(), validateStaticDomainType, getStaticEntityDomain);
router.delete("/:path/entitydomain/static", authenticate, authorize, decodePid(), auditable, validateStaticDomainType, resetStaticEntityDomain);
router.put("/entitydomain/static/:id", authenticate, authorize, decodePid(), auditable, validateStaticDomainType, setStaticEntityDomain);
router.get("/entitydomain/static", authenticate, authorize, decodePid(), validateStaticDomainType, getStaticEntityDomain);
router.delete("/entitydomain/static", authenticate, authorize, decodePid(), auditable, validateStaticDomainType, resetStaticEntityDomain);

export default router;
